Kind,Underlying component,Name,Summary,Description,Inputs,Outputs,Properties,Notes,Prioritization notes
HoneycombExporter,,,,,,,,,
SendToArchive,,,,,,,,S3 Exporter configured for Enhance,
OTelHTTPExporter,,,,,,,,,
OTelGRPCExporter,,,,,,,,,
OtelDebugExporter,,,,,,,,,
FilterBySeverity,,,,,,,,,"Specifically requested for demo. Might require normalizing first, or perhaps the component itself includes some affordance for severity level definition (maybe in advanced)"
ParseJSON,,,,,,,,Takes an input field and adds the statements/merge_maps to replace the fields into attributes,Could (should) definitely do a processor for now. Could see combining with receivers (like filelog) when we have deamonset
Symbolicator,,,,,,,,Used with HFO,We should support HNY features out of the box
ColumnDeletion,,,,,,,,"For hiding sensitive info, or reducing noise",
RedactFields,,,,,,,,"Bindplane's implementation: https://bindplane.com/docs/resources/processors/mask-sensitive-data

Semantic gotcha: What we name this probably matters. Customers tend to search for ""redact"" even if that's not exactly what they want to do. ""Mask"" implies the ability to unmask. Other options: obfuscate",
RenameAttributes,,,,,,,,Change field keys based on a pattern,
NormalizeFields,,,,,,,,Patterned changes to multiple field values (find/replace),
ParseUserAgent,,,,,,,,,Parses a user agent string and breaks it out into attributes.
ChangeServiceName,,,,,,,,Group by processor for a span attribute and a transform processor to copy it to service.name,
LogDeduplicationProcessor,,,,,,,,,
FilterByTargetUrl,,,,,,,,"Filters spans based on the `http.target` field, used for filtering healthchecks. It could be made more specific by adding in all the different health type urls (/health, /healthz, etc.)",
HashField,,,,,,,,Hash a field (different to redact),"Valuable, but lower priority than Redact."
AllowOnlyTheseFields,,,,,,,,Apply an AllowList of fields,"Valuable, but since you can technically achieve similar results with ColumnDeletion (albeit with more effort, potentially), other processors are higher priority for now."
FilterLogsByLibrary,,,,,,,,Filter processor to delete logs based on the library,Sounds useful. Needs info on how we would intepret the library and corresponding behavior
k8sattributes,,,,,,,,,"Not restricted to daemonset, but its way more performant in a daemonset. When run in a Deployment each replica has to keep in memory all the metadata for the entire cluster instead of just 1 node."
OTelReceiver,,,,,,,,,
TraceConverter,,,,,,,,Needed to separate collector from refinery,"Even though this happens in the collector, placed this in the Refinery group because they go together semantically."
DeterministicSampler,,,,,,,,,
EMAThroughput,,,,,,,,,
EMA,,,,,,,,,
SampleErrors,,,Samples HTTP errors based on error codes,,,,,"it's a bad name, should probably be SampleStatus",
KeepSlowTraces,,,,,,,,Keep traces over a set duration,
KeepErrors,,,,,,,,"If an error field exists, set a sample rate",
DropBigTraces,,,,,,,,,
EvaluateBigTraceEarly,,,,,,,,,
SampleByURLPath,,,,,,,,,
SampleHealthChecks,,,,,,,,Sample health checks if they haven't already been dropped,
DropNonHTTP,,,,,,,,,"what else do we need related to grpc?
"
